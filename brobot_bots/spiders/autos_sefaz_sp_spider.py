# -*- coding: utf-8 -*-
# origin repository git sha: 523f91e2126a1742c4d01d6a3cd3412baa07a73d
import datetime
import os
import re
import sys

from bs4 import BeautifulSoup
from scrapy import signals
from scrapy.http import Request, FormRequest
from scrapy_splash import SplashRequest

from brobot_bots.external_modules import custom_messages
from brobot_bots.external_modules.custom_errors import (WrongCredentials,
                                                        SpiderIntercepted,
                                                        PageMalfunction,
                                                        InvalidParameters,
                                                        InvalidRequestParams,
                                                        UndefinedError,
                                                        KnownWebsiteBug,
                                                        Halt)
from brobot_bots.external_modules.external_functions import CustomSpider, trap_start_requests
from brobot_bots.external_modules.utils import extract_text
from brobot_bots.items import BrobotBotsItem

path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if path not in sys.path:
    sys.path.insert(1, path)
#del path


# fixme __comprovantes_de_acesso__ was removed at the start of 2022, when it is changed back to screenshot,
#  So if __comprovantes_de_acesso__ is needed again look for code before this point (ref sha: 6e35e28abc7afd4e8d1d7222aaecff89a637f215)

# fixme hcaptcha is removed in #1032, if it is back on site, you can check that PR to see what needs to be reversed regarding captcha

# The website is no longer returning a 403 error; it seems that Cloudflare has been disabled. If the spider starts returning 403 again, check this pr: https://github.com/brobot-br/brobot_bots/pull/1350/

class autos_sefaz_sp_spider(CustomSpider):
    # required scraper name
    name = "autos_sefaz_sp"

    # initial urls
    start_url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet_consulta/consulta.aspx'
    download_url = "https://www.ipva.fazenda.sp.gov.br/ipvanet/"

    login_lua_script = """
    function main(splash, args)
        splash:init_cookies(args.cookies)
        assert(splash:go(args.url))

        wait_for_element{css='#conteudoPaginaPlaceHolder_txtRenavam'}

        input_text('#conteudoPaginaPlaceHolder_txtRenavam', args.renavam)
        input_text('#conteudoPaginaPlaceHolder_txtPlaca', args.placa)
        click_button('#conteudoPaginaPlaceHolder_btn_Consultar')

        wait_for_element{css='#conteudoPaginaPlaceHolder_btn_Consultar', reverse=true, delay=20}
        wait_for_element{css='#conteudoPaginaPlaceHolder_btn_Voltar', delay=20}
        assert(splash:wait(5))
        
        splash:set_viewport_full()
        return {
            cookies = splash:get_cookies(),
            html = splash:html(),
            png = splash:png()
        }
    end

    """

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""

        spider = super(autos_sefaz_sp_spider, cls).from_crawler(
            crawler, *args, **kwargs)
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ipva_years_list = []
        self.wrong_user_agent_retries = 5
        self.retries = 7
        self.login_retries = 5
        self.validate_empty_keys = True
        self.use_user_agent_caching = True
        self.proxy_required = True
        self.proxy_service = 'TWOCAPTCHA'
        #self.proxy_service = 'BRIGHTDATA_RESIDENTIAL'
        self.pdf_request = []
        self.web_unlocker_url = None
        self.splash_retries = 3

             
        import debugpy
        debugpy.listen(('0.0.0.0', 4444))
        print("Waiting for debugger attach")
        debugpy.wait_for_client()


    @trap_start_requests
    def start_requests(self, reset_user_agent=False, reset_cookies=False):
        if not self.valid_credentials(['placa', 'renavam']):
            return
        
        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        meta = {
            'url_for_proxy': 'https://www.example.com/',
            'handle_httpstatus_list': [403, 400],
            'reset_user_agent': reset_user_agent,
            'reset_cookies': reset_cookies,
            'download_timeout': 160,
        }

        url = 'http://ip-api.com/json'
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        yield SplashRequest(
            url=url, 
            callback=self.parse_main_data, 
            errback=self.errback_splash_func,
            meta=meta,
            dont_filter=True
        )
        return

        yield SplashRequest(
            url=self.start_url, 
            callback=self.parse_main_data, 
            errback=self.errback_splash_func,
            endpoint='execute',
            meta=meta,
            args={
                'lua_source': self.load_lua_script(
                    script=self.login_lua_script,
                    lua_lib=["utils.lua"]
                ),
                "placa": self.placa,
                "renavam": self.renavam,
                "timeout": 150
            },
            dont_filter=True
        )

    def errback_splash_func(self, failure):
        if self.splash_retries > 0:
            self.splash_retries -= 1
            self._reset_proxy_session()
            yield from self.start_requests(reset_user_agent=True, reset_cookies=True)
            return
        self.errback_func(failure)
    
    def _reset_proxy_session(self):
        # Reset proxy session
        self.proxy_session = None
        self.proxy_resolved_server = None

    def _restart_requests_after_incorrect_captcha(self, captcha_solution_id):
        if not self.report_incorrect_captcha(captcha_solution_id):
            yield from self.start_requests()

    def parse_main_data(self, response, captcha_solution_id=None):
        breakpoint()
        # In production, the path is given by //span[@id='conteudoPaginaPlaceHolder_lblErro']/b/text()
        error_message = response.xpath("//span[@id='conteudoPaginaPlaceHolder_lblErro']//text()").get("").strip()
        if 'RENAVAM/PLACA NÃO ENCONTRADOS' in error_message:
            error = WrongCredentials(message='RENAVAM/PLACA NÃO ENCONTRADOS')
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return
        
        if 'Preencha o campo' in error_message or 'Renavam inválido' in error_message:
            error = InvalidParameters(message=error_message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return
        
        if 'The socket connection was aborted' in error_message:
            error = KnownWebsiteBug(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        if 'server error' in response.text.lower():
            error_message = "Server Error '/IPVANET_Consulta' Application"
            error = KnownWebsiteBug(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        if 'servico indisponivel para manutencao' in error_message.lower():
            error = KnownWebsiteBug(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        # after captcha is back on page, this undefined error needs to be moved after captcha check
        if error_message:
            error = UndefinedError(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        if "captcha" in error_message:
            yield from self._restart_requests_after_incorrect_captcha(captcha_solution_id)
            return
        
        if response.status == 403 or 'Esta requisição foi rejeitada' in extract_text(response):
            # If we get intercepted, it's probably Cloudflare, so we retry with the web unlocker and disable the proxy.
            if self.login_retries:
                self.login_retries -= 1
                self.logger.warning('Esta requisição foi rejeitada')
                yield from self.start_requests(reset_user_agent=True, reset_cookies=True)
            else:
                error = SpiderIntercepted(message=custom_messages.PAGE_MALFUNCTION, details=extract_text(response))
                self.logger.error(error)
                self.errors.append(error.to_dict())
            return
        
        renavam = response.xpath(
            "//span[@id='conteudoPaginaPlaceHolder_txtRenavam']/text()").get("").strip()
        
        # There's also a bug on the website where it sometimes returns the homepage instead of the successful login. ]
        # This happens in the browser too. The current workaround is to retry the request.
        if not renavam or 'dentro de instantes sua solicitação será redirecionada' in extract_text(response.xpath('//body')):
            if self.login_retries > 0:
                self.login_retries -= 1
                yield from self.start_requests(reset_user_agent=True, reset_cookies=True)
                return
            # Missing renavam in response
            error_msg = 'Website voltou à página de login, após informar placa e renavam. Por favor tente novamente.'
            error = PageMalfunction(message=error_msg)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        # Vehicle information
        cells = response.xpath("//span[@id='conteudoPaginaPlaceHolder_txtRenavam']/ancestor::table[1]//td[@align='right']")
        cells += response.xpath("//table[@id='tableComunicVendaDetail']//table//td[@align='right']")

        self.result.update(
            {self.remove_diacritics(extract_text(cell)): extract_text(cell.xpath('./following::td[1]')) for cell in
             cells if self.remove_diacritics(extract_text(cell))})
        observacoes = extract_text(response.xpath("//table[@id='tableComunicVendaMaster']"))
        if observacoes:
            self.result.update({'observacoes': observacoes})
        # parse multas, ipva, etc data and update in result
        tables = response.xpath("//div[@id='conteudoPaginaPlaceHolder_Panel1']/table[.//td[@class='alinharEsquerda, negrito' and ./span[contains(@id,'conteudoPaginaPlaceHolder_Label') and not(text()=' ')]]]")

        for table in tables[1:]:
            table_name = " ".join([t.strip() for t in table.xpath(".//td[@class='alinharEsquerda, negrito' and ./span[contains(@id,'conteudoPaginaPlaceHolder_Label') and not(text()=' ')]]/span/text()").extract()])
            table_name = self.remove_diacritics(table_name)

            # Sometimes website returns incomplete/empty html in response when parsing main data 
            if table_name == "":
                if self.retries > 0:
                    self.retries -= 1
                    yield from self.start_requests()
                    return
                # At least one table is empty in result. Please try again.
                error = PageMalfunction(details="A página não carregou corretamente uma das tabelas. Por favor, tente novamente.")
                self.logger.error(error)
                self.errors.append(error.to_dict())
                return

            main_table = table.xpath("./following::table[@class='loginTable' and .//tr[not(@class) and ./td[not(@class)]/span]][1]")
            rows = main_table.xpath(".//tr[not(@class)]")

            if re.match(r"ipva_\d{4}", table_name) or table_name == 'ipva':
                # get data from 'ipva' table or 'ipva_<year>'
                ipva_table_name = table_name
                table_content = {}
                for row in rows:
                    title = self.remove_diacritics(row.xpath("./td[1]/span/text()").get("").strip())
                    value = extract_text(row.xpath("./td[last()]/span"))
                    if title and 'atencao_1' not in title:
                        table_content.update({title: value})
                self.result.update({table_name: table_content})
                # Get data from pagamento_de_debitos table, table name is ipva_table_name + pagamento_de_debitos_table_name
                second_table = main_table.xpath("./following::table[@class='loginTable'][1]//tr[1]/td/span")
                st_name = self.remove_diacritics(second_table.xpath("./text()").get("").strip())
                st_name = self.remove_diacritics(f'{ipva_table_name}_{st_name}')
                st_rows = second_table.xpath("./following::table[@class='loginTable'][1]//tr")
                table_content = []
                for row in st_rows[1:]:
                    title = extract_text(row.xpath("./td[1]/span"))
                    date = row.xpath("./td[2]/span/text()").get("").strip()
                    value = re.sub('\s+', " ", " ".join(
                        row.xpath("./td[last()]/span/text()").extract()).strip())
                    if title and date:
                        table_content.append({
                            'modalidades_disponiveis': title,
                            'vencimento': date,
                            'valor': value
                        })
                if table_content:
                    self.result.update({st_name: table_content})

                # PAGAMENTOS EFETUADOS
                efetuados_table = response.xpath('//table[@id="conteudoPaginaPlaceHolder_tbPagtosEfetuados"]')
                if not efetuados_table:
                    continue
                efetuados_name = self.remove_diacritics(extract_text(efetuados_table.xpath("//span[contains(@id,'TituloPagtosEfetuados')]")))

                header_row = [self.remove_diacritics(extract_text(column)) for column in efetuados_table.xpath(".//tr[2]/td")]

                if 'nada consta' in extract_text(efetuados_table).lower():
                    nada_consta = dict(zip(header_row, [''] * 10))
                    nada_consta.update({'msg': 'NADA CONSTA'})
                    self.result[efetuados_name] = [nada_consta]
                    continue

                def extract_current_row(row):
                    return [extract_text(i) for i in row.xpath("./td")]
                rows_data = [dict(zip(header_row, extract_current_row(row))) for row in efetuados_table.xpath('.//tr[position() > 3]')]
                self.result[efetuados_name] = rows_data

            elif table_name == "taxas":
                table_content = {}
                taxas_type = rows[0].xpath("./td[1]/span/text()").get("").strip()
                table_content.update({'type': taxas_type})
                # workaround for different types
                is_nada_costa = rows[1].xpath(".//span[contains(text(),'NADA CONSTA')]")
                i = 1 if is_nada_costa else 2
                for row in rows[i:]:
                    if 'display:none' not in row.xpath("./@style").get(""):
                        title = self.remove_diacritics(row.xpath("./td[1]/span/text()").get("").strip())
                        value = row.xpath("./td[last()]/span/text()").get("").strip()
                        if title:
                            table_content.update({title: value})
                self.result.update({table_name: table_content})
            elif table_name == 'multas':
                table_name = 'multas_totais'
                if table_name not in self.result:
                    self.result[table_name] = []
                # check if it is 'Nada consta', if it is don't do anything, because page is in that case different
                if not response.xpath(".//span[@id='conteudoPaginaPlaceHolder_txtResumoMultas']"):
                    # add 'TOTAL' row to rows
                    total_row = response.xpath(".//table[@id='conteudoPaginaPlaceHolder_tbMultaTotais']//tr")
                    rows += total_row
                    header_row = [' '.join(i.xpath(".//text()").extract()).strip() for i in rows[0].xpath("./td")]
                    for row in rows[1:]:
                        cells = row.xpath("./td")
                        cells = [' '.join(cell.xpath(".//text()").extract()).strip() for cell in cells]
                        temp = {}
                        for i, cell in enumerate(cells):
                            key = self.remove_diacritics(header_row[i])
                            value = cell
                            if key not in ['']:
                                temp[key] = value
                        if temp:
                            self.result[table_name].append(temp)
            else:
                table_content = []
                for row in rows[1:]:
                    if 'display:none' not in row.xpath("./@style").get(""):
                        exercicio = row.xpath("./td[1]/span/text()").get("").strip()
                        valor = row.xpath("./td[last()]/span/text()").get("").strip()
                        is_valor_table = rows[0].xpath(".//span[contains(text(),'Valor')]")
                        if exercicio:
                            rows_data = {'exercicio': exercicio}
                            if is_valor_table:
                                rows_data.update({'valor': valor})
                            table_content.append(rows_data)
                self.result.update({table_name: table_content})

        self.cookies = response.data['cookies']
        if self.capture_screenshot:
            self.upload_screenshot_with_splash_engine(response)
            
        multas_btn = response.xpath("//input[contains(@id,'Multas')]")
        if multas_btn:
            # Get options for request
            multas_btn_name = multas_btn.xpath("./@name").get("").strip()
            multas_btn_value = multas_btn.xpath("./@value").get("").strip()
            frm_data = self._form_factory(response=response)
            frm_data.update({multas_btn_name: multas_btn_value})

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'pt-BR,pt;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://www.ipva.fazenda.sp.gov.br',
                'Pragma': 'no-cache',
                'Referer': 'https://www.ipva.fazenda.sp.gov.br/ipvanet_consulta/Pages/Aviso.aspx',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': self.user_agent,
            }

            multas_url = "https://www.ipva.fazenda.sp.gov.br/ipvanet_consulta/Pages/aviso.aspx"
            yield FormRequest(
                url=multas_url,
                formdata=frm_data,
                callback=self.parse_multas_details,
                errback=self.errback_func,
                dont_filter=True,
                headers=headers,
                cookies=self.cookies
            )

        def check_if_to_download_ipva():
            # as we we only interested if value is bigger than 0 we can remove all comma and full stops as those don't matter
            ipva_value = self.result.get("ipva", {}).get("10_valor_devido_nao_inscrito_em_divida_ativa_10_8_9 ", '0').replace(',', '').replace(',', '')
            if ipva_value and float(ipva_value) > 0:
                return True
            # Add the current year + 1 to get data for 2024, which appears before the current year ends
            current_year = datetime.date.today().year + 1
            #self.result["year"] = current_year
            for year in [current_year - i for i in range(0, 10)]:
                ipva_value = self.result.get(f"ipva_{year}", {}).get("10_valor_devido_nao_inscrito_em_divida_ativa_10_8_9", '0')
                if ipva_value and float(ipva_value.replace(',', '').replace(',', '')) > 0:
                    return True
            for debito in self.result.get('ipva_debitos_nao_inscritos', []):
                if debito.get('valor', '0') and float(debito.get('valor', '0').replace(',', '').replace(',', '')) > 0:
                    return True

        # We are not downloading IPVA if get_boletos == False
        if not self.get_boletos or not check_if_to_download_ipva():
            return
        
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': self.user_agent,
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        meta = {
            'pdf_index': 0
        }
        # We make request for IPVA no matter on get_boletos.
        yield Request(
            url=self.download_url,
            headers=headers,
            callback=self.get_emissao_pagamento,
            errback=self.errback_func,
            dont_filter=True,
            meta=meta,
            cookies=self.extract_cookies(response, _for='splash_list')
        )

    def get_emissao_pagamento(self, response):
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return

        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'content-type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache',
            'Origin': 'https://www.ipva.fazenda.sp.gov.br',
            'Referer': response.url,
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': self.user_agent
        }

        submit_option = response.selector.xpath("//input[@id='btnOpcoes']/@value").get("")
        frm_data = self._form_factory(response=response)
        frm_data.update({
            'btnOpcoes': submit_option
        })

        yield FormRequest(
            url=self.download_url,
            formdata=frm_data,
            callback=self.gerar_guia_pagamento,
            errback=self.errback_func,
            headers=self.headers,
            meta=response.meta,
            cookies=self.extract_cookies(response, _for='splash_list'),
            dont_filter=True
        )

    def gerar_guia_pagamento(self, response):
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return

        rb_pag = response.selector.xpath("//input[@id='rbPag_2']/@value").get("")
        rb_proc = response.selector.xpath("//input[@id='rbProc_0']/@value").get("")
        submit_option = response.selector.xpath("//input[@id='btnConsultar']/@value").get("")

        frm_data = self._form_factory(response=response)
        frm_data.update({
            'rbPag': rb_pag,
            'rbProc': rb_proc,
            'ddVeiculo': '01',
            'btnConsultar': submit_option,
        })

        url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet/calculo.aspx'
        yield FormRequest(
            url=url,
            formdata=frm_data,
            callback=self.dados_do_veiculo_ipva,
            errback=self.errback_func,
            headers=self.headers,
            meta=response.meta,
            cookies=self.extract_cookies(response, _for='splash_list'),
            dont_filter=True
        )

    def dados_do_veiculo_ipva(self, response):
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return
        locadora = response.selector.xpath("//input[@id='rblLocadora_1']/@value").get("")
        submit_option = response.selector.xpath("//input[@id='btnConsultar']/@value").get("")

        frm_data = self._form_factory(response=response)
        frm_data.update({
            '__LASTFOCUS': '',
            'txRenavam': self.renavam,
            'txPlaca': self.placa,
            'rblLocadora': locadora,
            'btnConsultar': submit_option,
        })

        self.headers.update({
            'Referer': response.url,
        })

        url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet/guiater.aspx'
        yield FormRequest(
            url=url,
            formdata=frm_data,
            callback=self.get_lista_debitos,
            errback=self.errback_func,
            headers=self.headers,
            meta=response.meta,
            cookies=self.extract_cookies(response, _for='splash_list'),
            dont_filter=True
        )

    def get_lista_debitos(self, response):
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return
        
        pdf_index = response.meta.get('pdf_index')
        
        self.headers.update({
            'Referer': response.url,
        })
        
        debitos = response.selector.xpath('//table[@id="gwDebitosIpva"]/tr[position()>1]')

        if len(debitos) < 2:            
            self.result.setdefault("__ipva__", {})
            self.result["__ipva__"]['file_unavailable'] = "Boleto não disponível para o valor devido."
            return
        
        if pdf_index > 0:
            yield from self.process_debitos(response, debito=debitos[pdf_index])
            return
        # If there are more than one 'boleto', we re-authenticate and process again
        for index, debito in enumerate(debitos):
            year = extract_text(debito.xpath('td[2]'))

            if 'Outro' in year:
                continue
            
            if index == 0: 
                yield from self.process_debitos(response, debito=debitos[pdf_index])
                continue
            
            self.pdf_request.append(
                Request(
                    url=self.download_url,
                    callback=self.get_emissao_pagamento,
                    errback=self.errback_func,
                    dont_filter=True,
                    meta={'pdf_index': index}
                )
            )
        
    def process_debitos(self, response, debito):
        year = extract_text(debito.xpath('td[2]'))
        input_element = debito.xpath('.//input[@type="radio"]')
        input_name = input_element.xpath('@name').get('')
        input_value = input_element.xpath('@value').get('')
        frm_data = self._form_factory(response=response)
        submit_option = response.selector.xpath("//input[@id='btnSelecionar']/@value").get("")
        frm_data.update({
            '__EVENTTARGET': '&'.join(input_name.split(':')),
            '__LASTFOCUS': '',
            input_name: input_value,
            'btnSelecionar': submit_option,
        })
    
        url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet/guiater_debitosonline.aspx'
        yield FormRequest(
            url=url,
            formdata=frm_data,
            callback=self.opcoes_pagamento,
            errback=self.errback_func,
            headers=self.headers,
            cookies=self.cookies,
            priority=1,
            meta={'year': year},
            dont_filter=True
        )     
        
    def opcoes_pagamento(self, response):      
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return

        valor = response.selector.xpath("//input[@id='txAdValor']/@value").get("")
        submit_option = response.selector.xpath("//input[@id='btnConsultar']/@value").get("")    

        frm_data = self._form_factory(response=response)
        frm_data.update({
            'txAdValor': valor,
            'btnConsultar': submit_option,
        })

        frm_data.pop('__EVENTTARGET')
        frm_data.pop('__EVENTARGUMENT')
    
        self.headers.update({
            'Referer': response.url,
        })

        url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet/guiater1.aspx'
        
        yield FormRequest(
            url=url,
            formdata=frm_data,
            callback=self.gerar_guia,
            errback=self.errback_func,
            headers=self.headers,            
            cookies=self.extract_cookies(response, _for='splash_list'),
            meta=response.meta,
            dont_filter=True
        )
        
    def gerar_guia(self, response):
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return

        submit_option = response.selector.xpath("//input[@id='btnIpva']/@value").get("")    
        frm_data = self._form_factory(response=response)
        frm_data.update({
            'btnIpva': submit_option,
        })

        self.headers.update({
            'Referer': response.url,
        })

        url = 'https://www.ipva.fazenda.sp.gov.br/ipvanet/geraboleto.aspx'
        yield FormRequest(
            url=url,
            formdata=frm_data,
            callback=self.download_ipva_pdfs,
            errback=self.errback_func,
            headers=self.headers,
            cookies=self.extract_cookies(response, _for='splash_list'),
            meta=response.meta,
            dont_filter=True
        )

    def parse_multas_details(self, response):
        def find_key_value_pairs(cells):
            ordered_list = []
            for cell in cells:
                # We are checking if it is key by last character
                if cell and cell.strip()[-1] == ':':
                    ordered_list.append([self.remove_diacritics(cell)])
                elif cell:
                    ordered_list[-1].append(cell)

            # We need to make sure every sublist contains 2 elements
            ordered_list = [(item + [''])[:2] for item in ordered_list]
            return dict(ordered_list)

        # Keys: ['infracao', 'municipio', 'no_do_a_i_t', 'vencimento', 'local', 'no_da_guia', 'valor', 'data_hora', 'receita']
        multas_table = response.xpath(".//div[@id='conteudoPaginaPlaceHolder_pnlMultasDet']")
        multas = []
        multas_data = {}
        for row in multas_table.xpath('./table//tr'):
            cells = [extract_text(cell) for cell in row.xpath('./td')]
            if not any(item for item in cells) and multas_data:
                multas.append(multas_data)
                multas_data = {}
            elif find_key_value_pairs(cells):
                multas_data.update(find_key_value_pairs(cells))

        if not multas:
            if self.login_retries > 0:
                self.login_retries -= 1
                yield from self.start_requests(reset_user_agent=True, reset_cookies=True)
                return
            # Missing renavam in response
            error_msg = 'Website voltou à página de login, após informar placa e renavam. Por favor tente novamente.'
            error = PageMalfunction(message=error_msg, details='Multas page redirected to home page.')
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        self.result.update({'multas': multas})
        if self.capture_screenshot:
            self.upload_screenshot_with_splash_engine(response)

    
    def download_ipva_pdfs(self, response):
        """
            Download IPVA files
        """
        try:
            yield from self._assert_pdf_response(response)
        except Halt:
            return
            
        year = response.meta['year']
        self.result.setdefault("__ipva__", {})

        html = self._add_image_to_pdf(response.text)
        file_id = self.upload_pdf_with_splash_engine(
            html=html,
            base_url=self.download_url,
            ratio=0.8,
            margin=[35, 0, 0, 0]
        )

        if self.errors and f"{file_id}.pdf" in [self.errors[-1].get("file"), self.errors[-1].get("filename")]:
            self.errors.pop()
            file_data = {'file_unavailable': 'Órgão consultado não permitiu download do boleto neste momento.'}
        else:
            file_data = {}
            file_data.update({
                'file_id': file_id,
                'vencimento_do_boleto': response.selector.xpath("//span[contains(@id,'DataVenc1')]/text()").get('').replace('*', ''),
                'linha_digitavel_codigo_barras': response.selector.xpath("//span[contains(@id,'CodNum')]/text()").get(''),
                'parcela': response.selector.xpath("//span[contains(@id,'TextoParc')]/text()").get('')
            })

        self.result["__ipva__"].update({year: file_data})


    def _add_image_to_pdf(self, html_response):
        # This is because splash may sometimes not load all images correctly for barcode
        # white image
        white_image = 'R0lGODlhCgBQAIAAAP///////yH5BAUUAAEALAAAAAAKAFAAAAIahI+py+0Po5y02ouz3rz7D4biSJbmiabqihUAOw=='
        # black image
        black_image = 'R0lGODlhCgBQAIAAAAAAAP///yH5BAUUAAEALAAAAAAKAFAAAAIahI+py+0Po5y02ouz3rz7D4biSJbmiabqihUAOw=='
        soup = BeautifulSoup(html_response, "html.parser")
        selector_images = [("img[src*='/p.gif']", black_image),
                           ("img[src*='/b.gif']", white_image)]
        for sel, img in selector_images:
            p_script_tags = soup.select(sel)
            for element in p_script_tags:
                element['src'] = "data:image/png;base64," + img

        return str(soup)

    def _assert_pdf_response(self, response):
        if 'dentro de instantes sua solicitação será redirecionada.' in extract_text(response.xpath('//body')):
            if self.retries:
                self.retries -= 1
                yield response.request.replace(dont_filter=True)
                raise Halt()
            self._set_file_unavailable(response=response)
            raise Halt()
        
        if 'Object reference not set to an instance' in response.text or 'Digite um exercício válido' in response.text:
            self._set_file_unavailable(response=response)
            raise Halt()
        
    def _set_file_unavailable(self, response):
        error_message = "Órgão consultado não permitiu download do boleto neste momento. Por favor, tente mais tarde."
        self.result.setdefault("__ipva__", {})
        if year := response.meta.get('year'):
            self.result["__ipva__"].update({year: {'file_unavailable': error_message}})
        else:
            self.result["__ipva__"]['file_unavailable'] = error_message

    def _form_factory(self, response):
        event_target = response.selector.xpath("//input[@id='__EVENTTARGET']/@value").get("")
        event_argument = response.selector.xpath("//input[@id='__EVENTARGUMENT']/@value").get("")
        view_state = response.selector.xpath("//input[@id='__VIEWSTATE']/@value").get("")
        view_state_generator = response.selector.xpath("//input[@id='__VIEWSTATEGENERATOR']/@value").get("")
        event_validation = response.selector.xpath("//input[@id='__EVENTVALIDATION']/@value").get("")
        
        frm_data = {
            '__EVENTTARGET': event_target,
            '__EVENTARGUMENT': event_argument,
            '__VIEWSTATE': view_state,
            '__VIEWSTATEGENERATOR': view_state_generator,
            '__EVENTVALIDATION': event_validation,
        }
        
        return frm_data
        
    def get_final_result(self, spider):
        """Will be called before spider closed
        Used to save data_collected result."""

        if not self.errors and self.pdf_request:
            req = self.pdf_request.pop()
            self.crawler.engine.crawl(req, spider)
            return
        
        # stop crawling after yeild_item called
        if not self.result_received:
            # push to webhook
            if self.screenshots_ids:
                self.result['__screenshots_ids__'] = self.screenshots_ids
            self.data = {
                'scrape_id': self.scrape_id,
                'scraper_name': self.name,
                'files_count': self.files_count,
                'screenshots_count': self.screenshots_count,
                'cnpj': self.cnpj}
            self.data.update({'result': self.result})
            if self.errors:
                self.data.update({'errors': self.unique_list(self.errors)})
            webhook_file_path = os.path.join(
                path, "downloads", self.scrape_id, '{renavam}-data_collected.json'.format(
                    renavam=self.renavam))
            self.data_collected(self.data, webhook_file_path)
            # return item for scrapinghub
            self.result_received = True
            req = Request(
                'http://example.com',
                callback=self.yield_item,
                errback=self.yield_item,
                dont_filter=True
            )
            self.crawler.engine.crawl(req, spider)


    def yield_item(self, response):
        """Function is using to yield Scrapy Item
        Required for us to see the result in ScrapingHub"""
        item = BrobotBotsItem()
        item.update(self.data)
        yield item